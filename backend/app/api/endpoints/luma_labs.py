"""
API endpoints for Luma Labs video generation using Dream Machine API.
"""

import logging
import base64
import io
from PIL import Image
from fastapi import APIRouter, Depends, HTTPException, Form, UploadFile, File, Query
from fastapi.responses import Response
from typing import Optional
import httpx

from app.core.auth import verify_api_key
from app.services.luma_service import luma_service
from app.schemas.luma import (
    FrontendLumaResponse,
    LumaGenerationStatusResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()

async def upload_image_to_temp_host(image_content: bytes) -> str:
    """
    Upload image to a temporary hosting service for public access.
    Returns a public URL that can be accessed by Luma Labs.
    """
    try:
        # Process image first
        image = Image.open(io.BytesIO(image_content))

        # Convert to RGB if necessary (removes transparency issues)
        if image.mode in ('RGBA', 'LA', 'P'):
            # Create white background for transparent images
            background = Image.new('RGB', image.size, (255, 255, 255))
            if image.mode == 'P':
                image = image.convert('RGBA')
            if image.mode in ('RGBA', 'LA'):
                background.paste(image, mask=image.split()[-1] if len(image.split()) > 3 else None)
            image = background
        elif image.mode != 'RGB':
            image = image.convert('RGB')

        # Optimize image size and quality
        width, height = image.size
        max_dimension = 1024  # Reasonable max for API

        if max(width, height) > max_dimension:
            # Resize maintaining aspect ratio
            if width > height:
                new_width = max_dimension
                new_height = int(height * (max_dimension / width))
            else:
                new_height = max_dimension
                new_width = int(width * (max_dimension / height))

            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            logger.info(f"Resized image from {width}x{height} to {new_width}x{new_height}")

        # Save optimized image to bytes
        output_buffer = io.BytesIO()
        image.save(output_buffer, format='JPEG', quality=85, optimize=True)
        optimized_content = output_buffer.getvalue()

        logger.info(f"Processed image: original size {len(image_content)} bytes, optimized size {len(optimized_content)} bytes")

        # Create base64 content for all services
        base64_content = base64.b64encode(optimized_content).decode('utf-8')

        # Try imgur.com (anonymous upload)
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # Imgur anonymous upload
                headers = {
                    "Authorization": "Client-ID 546c25a59c58ad7"  # Public anonymous client ID
                }

                data = {
                    "image": base64_content,
                    "type": "base64"
                }

                response = await client.post(
                    "https://api.imgur.com/3/image",
                    headers=headers,
                    data=data
                )

                if response.status_code == 200:
                    result = response.json()
                    if result.get("success") and result.get("data", {}).get("link"):
                        image_url = result["data"]["link"]
                        logger.info(f"Image uploaded successfully to Imgur: {image_url}")
                        return image_url
                else:
                    logger.warning(f"Imgur upload failed with status {response.status_code}: {response.text}")

        except Exception as e:
            logger.warning(f"Failed to upload to Imgur: {e}")

        # Try postimages.org as backup
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                files = {"upload": ("image.jpg", optimized_content, "image/jpeg")}
                data = {"optsize": "0"}

                response = await client.post(
                    "https://postimages.org/json/rr",
                    files=files,
                    data=data
                )

                if response.status_code == 200:
                    result = response.json()
                    if result.get("status") == "OK" and result.get("url"):
                        image_url = result["url"]
                        logger.info(f"Image uploaded successfully to PostImages: {image_url}")
                        return image_url

        except Exception as e:
            logger.warning(f"Failed to upload to PostImages: {e}")

        # If all hosting services fail, create a fallback solution
        logger.warning("All hosting services failed, using fallback")
        base64_content = base64.b64encode(optimized_content).decode('utf-8')
        return f"data:image/jpeg;base64,{base64_content}"

    except Exception as e:
        logger.error(f"Error processing/uploading image: {e}")
        # Final fallback: return original as base64
        base64_content = base64.b64encode(image_content).decode('utf-8')
        return f"data:image/jpeg;base64,{base64_content}"


@router.post(
    "/generate-text-to-video",
    response_model=FrontendLumaResponse,
    dependencies=[Depends(verify_api_key)],
)
async def generate_text_to_video(
    prompt: str = Form(..., description="Text description of the video"),
    resolution: str = Form(default="720p", description="Video resolution: 720p, 1080p, 4K"),
    duration: int = Form(default=5, description="Video duration in seconds (1-10)"),
    aspect_ratio: str = Form(default="16:9", description="Aspect ratio: 16:9, 1:1, 9:16"),
    loop: bool = Form(default=False, description="Whether video should loop"),
    concepts: Optional[str] = Form(default=None, description="Special effects (comma-separated)")
) -> FrontendLumaResponse:
    """Generate a video from text using Luma Labs Dream Machine."""

    try:
        logger.info(f"🎬 Generating text-to-video with Luma Labs: {prompt[:100]}...")

        # Validate inputs
        if not luma_service.validate_resolution(resolution):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid resolution. Must be one of: 720p, 1080p, 4K"
            )

        if not luma_service.validate_aspect_ratio(aspect_ratio):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid aspect ratio. Must be one of: 16:9, 1:1, 9:16"
            )

        if duration not in [5, 9]:
            raise HTTPException(
                status_code=400,
                detail="Duration must be 5 or 9 seconds (Luma Labs API limitation)"
            )

        # Parse concepts if provided
        concepts_list = None
        if concepts:
            concepts_list = [c.strip() for c in concepts.split(",") if c.strip()]

        # Call the service
        service_response = await luma_service.generate_text_to_video(
            prompt=prompt,
            resolution=resolution,
            duration=duration,
            aspect_ratio=aspect_ratio,
            loop=loop,
            concepts=concepts_list
        )

        # Convert to frontend response
        return FrontendLumaResponse.from_service_response(service_response)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in generate_text_to_video endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during video generation: {e}"
        )


@router.post(
    "/generate-image-to-video",
    response_model=FrontendLumaResponse,
    dependencies=[Depends(verify_api_key)],
)
async def generate_image_to_video(
    prompt: str = Form(..., description="Text description of the video"),
    resolution: str = Form(default="720p", description="Video resolution: 720p, 1080p, 4K"),
    duration: int = Form(default=5, description="Video duration in seconds (1-10)"),
    aspect_ratio: str = Form(default="16:9", description="Aspect ratio: 16:9, 1:1, 9:16"),
    loop: bool = Form(default=False, description="Whether video should loop"),
    keyframe_type: str = Form(..., description="Keyframe type: frame0, frame1, or both"),
    frame0: Optional[UploadFile] = File(default=None, description="Initial frame image"),
    frame1: Optional[UploadFile] = File(default=None, description="Final frame image")
) -> FrontendLumaResponse:
    """Generate a video from image keyframes using Luma Labs Dream Machine."""

    try:
        logger.info(f"🎬 Generating image-to-video with Luma Labs: {prompt[:100]}...")

        # Validate inputs
        if not luma_service.validate_resolution(resolution):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid resolution. Must be one of: 720p, 1080p, 4K"
            )

        if not luma_service.validate_aspect_ratio(aspect_ratio):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid aspect ratio. Must be one of: 16:9, 1:1, 9:16"
            )

        if duration not in [5, 9]:
            raise HTTPException(
                status_code=400,
                detail="Duration must be 5 or 9 seconds (Luma Labs API limitation)"
            )

        if keyframe_type not in ["frame0", "frame1", "both"]:
            raise HTTPException(
                status_code=400,
                detail="keyframe_type must be 'frame0', 'frame1', or 'both'"
            )

        # Validate required files based on keyframe type
        if keyframe_type == "frame0" and not frame0:
            raise HTTPException(status_code=400, detail="frame0 image is required")
        if keyframe_type == "frame1" and not frame1:
            raise HTTPException(status_code=400, detail="frame1 image is required")
        if keyframe_type == "both" and (not frame0 or not frame1):
            raise HTTPException(status_code=400, detail="Both frame0 and frame1 images are required")

        # Process uploaded images and create temporary URLs
        keyframes = {}

        if frame0:
            # Validate file type
            if not frame0.content_type or not frame0.content_type.startswith("image/"):
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid frame0 file type: {frame0.content_type}"
                )

            # Process image and upload to get public URL
            frame0_content = await frame0.read()
            frame0_url = await upload_image_to_temp_host(frame0_content)
            keyframes["frame0"] = {
                "type": "image",
                "url": frame0_url
            }

        if frame1:
            # Validate file type
            if not frame1.content_type or not frame1.content_type.startswith("image/"):
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid frame1 file type: {frame1.content_type}"
                )

            # Process image and upload to get public URL
            frame1_content = await frame1.read()
            frame1_url = await upload_image_to_temp_host(frame1_content)
            keyframes["frame1"] = {
                "type": "image",
                "url": frame1_url
            }

        # Call the service
        service_response = await luma_service.generate_image_to_video(
            prompt=prompt,
            keyframes=keyframes,
            resolution=resolution,
            duration=duration,
            aspect_ratio=aspect_ratio,
            loop=loop
        )

        # Convert to frontend response
        return FrontendLumaResponse.from_service_response(service_response)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in generate_image_to_video endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during video generation: {e}"
        )


@router.get(
    "/status/{generation_id}",
    response_model=LumaGenerationStatusResponse,
    dependencies=[Depends(verify_api_key)],
)
async def get_generation_status(generation_id: str) -> LumaGenerationStatusResponse:
    """Get the status of a video generation."""
    
    try:
        logger.info(f"📊 Getting status for generation: {generation_id}")

        service_response = await luma_service.get_generation_status(generation_id)

        return LumaGenerationStatusResponse(
            success=service_response.get("success", False),
            generation_id=generation_id,
            status=service_response.get("status", "unknown"),
            data=service_response.get("data"),
            error=service_response.get("error")
        )
        
    except Exception as e:
        logger.error(f"Error in get_generation_status endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error getting generation status: {e}"
        )


@router.get("/download-video")
async def download_video(
    url: str = Query(..., description="The video URL to download")
):
    """
    Proxy endpoint to download videos from Luma Labs
    and serve them directly to bypass CORS restrictions.
    """
    try:
        logger.info(f"📥 Proxying video download from: {url[:100]}...")

        # Validate that the URL is from a trusted source (Luma Labs)
        if not (url.startswith("https://storage.googleapis.com/luma-dream-machine") or
                url.startswith("https://storage.cdn-luma.com/")):
            raise HTTPException(
                status_code=400,
                detail="Only Luma Labs video URLs are allowed"
            )

        async with httpx.AsyncClient(timeout=300.0) as client:
            # Stream the video content
            async with client.stream("GET", url) as response:
                if response.status_code != 200:
                    logger.error(f"Failed to download video: {response.status_code}")
                    raise HTTPException(
                        status_code=response.status_code,
                        detail=f"Failed to download video: {response.status_code}"
                    )

                # Get content type from the response
                content_type = response.headers.get("content-type", "video/mp4")

                # Determine file extension based on content type
                if "mp4" in content_type:
                    file_extension = "mp4"
                elif "webm" in content_type:
                    file_extension = "webm"
                elif "mov" in content_type:
                    file_extension = "mov"
                else:
                    file_extension = "mp4"  # Default fallback

                # Create filename with timestamp
                import time
                filename = f"video-{int(time.time())}.{file_extension}"

                # Read the entire content
                content = b""
                async for chunk in response.aiter_bytes():
                    content += chunk

                # Return the video as a downloadable response
                return Response(
                    content=content,
                    media_type=content_type,
                    headers={
                        "Content-Disposition": f"attachment; filename={filename}",
                        "Content-Length": str(len(content)),
                        "Cache-Control": "no-cache"
                    }
                )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in download_video endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during video download: {e}"
        )



